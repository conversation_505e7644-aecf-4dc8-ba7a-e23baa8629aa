@tailwind base;
@tailwind components;
@tailwind utilities;

/* ===== COLORS ONLY - NO SIZING/SPACING/LAYOUT ===== */
:root {
  /* Brand Colors - ONE SHADE ONLY per Holy Rule 3 */
  --primary: #059669;
  
  /* Text Colors */
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  
  /* Background Colors - Apple Mac Desktop Style */
  --bg-primary: #fafafa;
  --bg-secondary: #f5f5f7;
  --bg-accent: #f0fdf4;
  
  /* Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-error: #fecaca;

  /* Focus Colors */
  --focus-shadow: rgba(5, 150, 105, 0.1);

  /* Error Colors */
  --error: #ef4444;
  --text-error: #dc2626;
  --bg-error: #fef2f2;

  /* Success Colors */
  --success: #10b981;
  --text-success: #10b981;
  --bg-success: #ecfdf5;

  /* Overlay Colors */
  --overlay-dark: rgba(0, 0, 0, 0.5);

  /* Shadow Colors - <PERSON> Mac Desktop Style */
  --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 6px rgba(0, 0, 0, 0.1);
  --shadow-large: 0 10px 15px rgba(0, 0, 0, 0.1);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-card-hover: 0 8px 30px rgba(0, 0, 0, 0.12), 0 2px 6px rgba(0, 0, 0, 0.15);
  --shadow-icon: 0 4px 12px rgba(0, 0, 0, 0.15);

  /* Warning Colors */
  --warning: #f59e0b;
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ===== FONT CHARACTER ONLY - NO SIZING ===== */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

/* ===== ICON STYLES ONLY ===== */
.icon {
  color: var(--primary);
}

.icon-secondary {
  color: var(--text-secondary);
}

/* ===== BUTTON COLOR STYLES ONLY ===== */
.button-primary {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.button-primary:hover {
  background-color: var(--primary);
  opacity: 0.9;
}

.button-secondary {
  background-color: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-medium);
}

.button-secondary:hover {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
}

/* ===== DROPDOWN LINK COLOR STYLES ONLY ===== */
.dropdown-link {
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.dropdown-link:hover {
  background-color: var(--bg-secondary);
}

/* ===== DASHBOARD CARD COLOR STYLES ONLY ===== */
.dashboard-card {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.dashboard-card:hover {
  background-color: var(--bg-primary);
}

/* ===== SIDEBAR BUTTON COLOR STYLES ONLY ===== */
.sidebar-button {
  color: var(--text-secondary);
  background-color: transparent;
}

.sidebar-button:hover {
  background-color: var(--bg-secondary);
  color: var(--primary);
}

.sidebar-button.active {
  background-color: var(--bg-accent);
  color: var(--primary);
}

/* ===== MACOS SIDEBAR NAVIGATION COLOR STYLES ONLY ===== */
.macos-sidebar-item {
  color: var(--text-primary);
  background-color: transparent;
}

.macos-sidebar-item:hover {
  background-color: var(--bg-secondary);
}

.macos-sidebar-item.active {
  background-color: var(--primary);
  color: var(--bg-primary);
}

.macos-sidebar-item.active:hover {
  background-color: var(--primary);
  color: var(--bg-primary);
}

/* ===== APPLE MAC DESKTOP LAYOUT COLOR STYLES ONLY ===== */
.macos-dashboard-layout {
  background-color: var(--bg-secondary);
}

.macos-sidebar {
  background-color: var(--bg-primary);
  border-color: var(--border-light);
}

.macos-main-content {
  background-color: var(--bg-primary);
}

.macos-content-area {
  background-color: var(--bg-secondary);
}

/* ===== APPLE MAC DESKTOP FOCUS STYLES ===== */
.focus-outline {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.focus-shadow {
  box-shadow: 0 0 0 3px var(--focus-shadow);
}
