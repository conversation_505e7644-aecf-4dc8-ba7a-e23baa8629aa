import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import { User, Calendar, MessageSquare, Heart, Bell, Settings } from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  full_name?: string
  avatar_url?: string
  role?: string
  created_at?: string
}

export default function Dashboard() {
  const navigate = useNavigate()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [searchQuery, setSearchQuery] = useState('')
  const [activityFilter, setActivityFilter] = useState('all')
  const [dashboardStats, setDashboardStats] = useState({
    upcomingAppointments: 0,
    unreadMessages: 0,
    careGroupsCount: 0,
    savedProviders: 0,
    completedAppointments: 0,
    activeConversations: 0,
    healthGoalsProgress: 0,
    medicationReminders: 0
  })

  // Memoized dashboard stats for performance optimization
  const memoizedStats = useMemo(() => ({
    appointmentProgress: Math.min((dashboardStats.upcomingAppointments / 10) * 100, 100),
    messageProgress: Math.min((dashboardStats.unreadMessages / 20) * 100, 100),
    messagePriority: dashboardStats.unreadMessages > 5 ? 'high' : 'normal',
    careGroupProgress: Math.min((dashboardStats.careGroupsCount / 50) * 100, 100),
    healthProgress: Math.min((dashboardStats.healthGoalsProgress / 100) * 100, 100)
  }), [dashboardStats])

  // Optimized tab switching with useCallback
  const handleTabSwitch = useCallback((tab: string) => {
    setActiveTab(tab)
  }, [])

  // Optimized search handler
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query)
  }, [])

  // Optimized filter handler
  const handleFilterChange = useCallback((filter: string) => {
    setActivityFilter(filter)
  }, [])

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session?.user) {
        navigate('/auth')
        return
      }

      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()

      if (profile) {
        setUser({
          id: profile.id,
          email: profile.email || session.user.email || '',
          first_name: profile.first_name,
          last_name: profile.last_name,
          full_name: profile.full_name,
          avatar_url: profile.avatar_url,
          role: profile.role,
          created_at: profile.created_at
        })
        
        // Load dashboard stats
        await loadDashboardStats(session.user.id)
      }
    } catch (error) {
      console.error('Error checking user:', error)
      setError('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  const loadDashboardStats = async (userId: string) => {
    try {
      // Get upcoming appointments count
      const { data: appointments } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'confirmed')
        .gte('start_time', new Date().toISOString())

      // Get unread messages count
      const { data: messagesData } = await supabase
        .from('messages')
        .select('id')
        .eq('recipient_id', userId)
        .eq('is_read', false)

      const unreadMessages = messagesData?.length || 0

      // Get saved providers count
      const { data: savedProvidersData } = await supabase
        .from('saved_providers')
        .select('id')
        .eq('user_id', userId)

      // Get care groups count
      const { data: careGroupsData } = await supabase
        .from('care_group_members')
        .select('care_group_id')
        .eq('user_id', userId)

      // Get completed appointments count
      const { data: completedAppointments } = await supabase
        .from('bookings')
        .select('id')
        .eq('user_id', userId)
        .eq('status', 'completed')

      setDashboardStats({
        upcomingAppointments: appointments?.length || 0,
        unreadMessages,
        careGroupsCount: careGroupsData?.length || 0,
        savedProviders: savedProvidersData?.length || 0,
        completedAppointments: completedAppointments?.length || 0,
        activeConversations: 0,
        healthGoalsProgress: 0,
        medicationReminders: 0
      })
    } catch (error) {
      console.error('Error loading dashboard stats:', error)
      setError('Failed to load dashboard statistics')
    }
  }

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      navigate('/')
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
            <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
          </div>
          <h2 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>Loading Your Dashboard</h2>
          <p className="text-lg" style={{ color: 'var(--text-secondary)' }}>Please wait while we load your data...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center max-w-md mx-auto p-6">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-error)' }}>
            <div className="w-8 h-8 rounded-full" style={{ backgroundColor: 'var(--text-error)' }}></div>
          </div>
          <h2 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>Connection Error</h2>
          <p className="text-base font-medium leading-relaxed mb-6" style={{ color: 'var(--text-secondary)' }}>{error}</p>
          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4 justify-center">
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 rounded-xl font-bold text-sm transition-all duration-200"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)'
              }}>
              Try Again
            </button>
            <button
              onClick={handleSignOut}
              className="px-6 py-3 rounded-xl font-bold text-sm border transition-all duration-200"
              style={{ borderColor: 'var(--border-medium)', color: 'var(--text-primary)' }}
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Apple Mac Desktop Style Sidebar */}
      <div className="w-64 flex-shrink-0 border-r"
           style={{
             backgroundColor: 'var(--bg-primary)',
             borderColor: 'var(--border-light)',
             boxShadow: 'var(--shadow-medium)'
           }}>
        {/* Sidebar Header */}
        <div className="p-6 border-b" style={{ borderColor: 'var(--border-light)' }}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'var(--primary)' }}>
              <Heart className="w-6 h-6" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h2 className="text-xl font-bold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
              CareConnect
            </h2>
          </div>
        </div>

        {/* Navigation Menu */}
        <nav className="p-4">
          <div className="space-y-2">
            <button
              onClick={() => setActiveTab('overview')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'overview' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'overview' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <User className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Overview</span>
            </button>
            <button
              onClick={() => setActiveTab('appointments')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'appointments' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'appointments' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <Calendar className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Appointments</span>
            </button>
            <button
              onClick={() => setActiveTab('messages')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'messages' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'messages' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <MessageSquare className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Messages</span>
              {dashboardStats.unreadMessages > 0 && (
                <span className="ml-auto bg-red-500 text-white text-xs px-2 py-1 rounded-full">
                  {dashboardStats.unreadMessages}
                </span>
              )}
            </button>
            <button
              onClick={() => setActiveTab('care-groups')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'care-groups' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'care-groups' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <Heart className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Care Groups</span>
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'notifications' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'notifications' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <Bell className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Notifications</span>
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors"
              style={{
                backgroundColor: activeTab === 'settings' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'settings' ? 'var(--bg-primary)' : 'var(--text-primary)'
              }}>
              <Settings className="w-5 h-5" />
              <span className="font-semibold text-sm tracking-wide">Settings</span>
            </button>
          </div>
        </nav>

        {/* User Profile Section */}
        <div className="absolute bottom-0 left-0 right-0 p-4 border-t" style={{ borderColor: 'var(--border-light)' }}>
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
              <User className="w-5 h-5" style={{ color: 'var(--text-primary)' }} />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-semibold truncate" style={{ color: 'var(--text-primary)' }}>
                {user?.first_name || user?.email}
              </p>
              <button
                onClick={handleSignOut}
                className="text-xs font-medium" style={{ color: 'var(--text-secondary)' }}>
                Sign out
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-hidden">
        <div className="h-full overflow-y-auto">
          {activeTab === 'overview' && (
            <div className="p-6 sm:p-8 lg:p-10">
              {/* Header */}
              <div className="mb-8 sm:mb-10 lg:mb-12">
                <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  Welcome back, {user?.first_name || 'there'}!
                </h1>
                <p className="text-lg sm:text-xl font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Here's what's happening with your care today.
                </p>
              </div>

              {/* Dashboard Stats Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 sm:mb-10 lg:mb-12">
                {/* Appointments Card */}
                <div className="p-6 rounded-2xl border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center justify-between mb-4">
                    <Calendar className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <span className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      {dashboardStats.upcomingAppointments}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
                    Upcoming Appointments
                  </h3>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="h-2 rounded-full" style={{ 
                      backgroundColor: 'var(--primary)', 
                      width: `${memoizedStats.appointmentProgress}%` 
                    }}></div>
                  </div>
                  <button
                    onClick={() => navigate('/bookings')}
                    className="text-sm font-semibold" style={{ color: 'var(--primary)' }}>
                    {dashboardStats.upcomingAppointments === 0 ? 'Schedule Appointment' : 'View Calendar'}
                  </button>
                </div>

                {/* Messages Card */}
                <div className="p-6 rounded-2xl border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center justify-between mb-4">
                    <MessageSquare className="w-8 h-8" style={{ color: memoizedStats.messagePriority === 'high' ? 'var(--bg-error)' : 'var(--primary)' }} />
                    <span className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      {dashboardStats.unreadMessages}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
                    Unread Messages
                  </h3>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="h-2 rounded-full" style={{ 
                      backgroundColor: memoizedStats.messagePriority === 'high' ? 'var(--bg-error)' : 'var(--primary)', 
                      width: `${memoizedStats.messageProgress}%` 
                    }}></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('messages')}
                    className="text-sm font-semibold" style={{ color: 'var(--primary)' }}>
                    {dashboardStats.unreadMessages === 0 ? 'View Messages' : 'Read Messages'}
                  </button>
                </div>

                {/* Care Groups Card */}
                <div className="p-6 rounded-2xl border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center justify-between mb-4">
                    <Heart className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <span className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      {dashboardStats.careGroupsCount}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
                    Care Groups
                  </h3>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="h-2 rounded-full" style={{ 
                      backgroundColor: 'var(--primary)', 
                      width: `${memoizedStats.careGroupProgress}%` 
                    }}></div>
                  </div>
                  <button
                    onClick={() => setActiveTab('care-groups')}
                    className="text-sm font-semibold" style={{ color: 'var(--primary)' }}>
                    {dashboardStats.careGroupsCount === 0 ? 'Join Groups' : 'View Groups'}
                  </button>
                </div>

                {/* Providers Card */}
                <div className="p-6 rounded-2xl border" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <div className="flex items-center justify-between mb-4">
                    <User className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                    <span className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>
                      {dashboardStats.savedProviders}
                    </span>
                  </div>
                  <h3 className="text-lg font-bold mb-2" style={{ color: 'var(--text-primary)' }}>
                    Saved Providers
                  </h3>
                  <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                    <div className="h-2 rounded-full" style={{ 
                      backgroundColor: 'var(--primary)', 
                      width: `${Math.min((dashboardStats.savedProviders / 10) * 100, 100)}%` 
                    }}></div>
                  </div>
                  <button
                    onClick={() => navigate('/professionals')}
                    className="text-sm font-semibold" style={{ color: 'var(--primary)' }}>
                    {dashboardStats.savedProviders === 0 ? 'Find Providers' : 'View Providers'}
                  </button>
                </div>
              </div>

              {/* Recent Activity Section */}
              <div className="mt-8 sm:mt-10 lg:mt-12">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-8 space-y-4 sm:space-y-0">
                  <h2 className="text-xl sm:text-2xl font-bold tracking-tight leading-tight" style={{ color: 'var(--text-primary)' }}>
                    Recent Activity
                  </h2>
                  <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-4">
                    <div className="relative">
                      <input
                        type="text"
                        placeholder="Search activity..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="w-full sm:w-64 px-4 py-2 rounded-xl border text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                        style={{
                          backgroundColor: 'var(--bg-primary)',
                          borderColor: 'var(--border-light)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>
                    <select
                      value={activityFilter}
                      onChange={(e) => setActivityFilter(e.target.value)}
                      className="px-4 py-2 rounded-xl border text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        borderColor: 'var(--border-light)',
                        color: 'var(--text-primary)'
                      }}>
                      <option value="all">All Activity</option>
                      <option value="appointments">Appointments</option>
                      <option value="messages">Messages</option>
                      <option value="care-groups">Care Groups</option>
                    </select>
                  </div>
                </div>

                {/* Activity Feed */}
                <div className="space-y-4">
                  {dashboardStats.completedAppointments === 0 && dashboardStats.unreadMessages === 0 && (
                    <div className="text-center py-12">
                      <Calendar className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                      <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                        No Recent Activity
                      </h3>
                      <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                        Your recent activities will appear here as you use the platform
                      </p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'appointments' && (
            <div className="p-6 sm:p-8 lg:p-10">
              <div className="text-center py-12">
                <Calendar className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Appointments Yet
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Your upcoming appointments will appear here
                </p>
              </div>
            </div>
          )}

          {activeTab === 'messages' && (
            <div className="p-6 sm:p-8 lg:p-10">
              <div className="text-center py-12">
                <MessageSquare className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Messages Yet
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Your messages will appear here
                </p>
              </div>
            </div>
          )}

          {activeTab === 'care-groups' && (
            <div className="p-6 sm:p-8 lg:p-10">
              <div className="text-center py-12">
                <Heart className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Care Groups Yet
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Join or create care groups to connect with your community
                </p>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="p-6 sm:p-8 lg:p-10">
              <div className="text-center py-12">
                <Bell className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  No Notifications
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  You're all caught up! Notifications will appear here
                </p>
              </div>
            </div>
          )}

          {activeTab === 'settings' && (
            <div className="p-6 sm:p-8 lg:p-10">
              <div className="text-center py-12">
                <Settings className="w-16 h-16 mx-auto mb-4" style={{ color: 'var(--text-secondary)' }} />
                <h3 className="text-2xl font-bold tracking-tight leading-tight mb-3" style={{ color: 'var(--text-primary)' }}>
                  Settings
                </h3>
                <p className="text-base font-medium leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                  Customize your account preferences and settings
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
