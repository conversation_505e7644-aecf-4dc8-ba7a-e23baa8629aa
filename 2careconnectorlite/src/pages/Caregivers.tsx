import { useState, useEffect } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { Search, MapPin, Star, Clock, Users, MessageCircle, Shield, Heart } from 'lucide-react'
import { dataService } from '../lib/dataService'

interface Caregiver {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability_status?: string
}

export default function Caregivers() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [caregivers, setCaregivers] = useState<Caregiver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchName, setSearchName] = useState('')
  const [searchLocation, setSearchLocation] = useState('')
  const [sortBy, setSortBy] = useState('name')
  const [maxRate, setMaxRate] = useState(200)
  const [favorites, setFavorites] = useState<string[]>([])

  const toggleFavorite = (caregiverId: string) => {
    setFavorites(prev =>
      prev.includes(caregiverId)
        ? prev.filter(id => id !== caregiverId)
        : [...prev, caregiverId]
    )
  }
  // Initialize search filters from URL parameters
  useEffect(() => {
    const locationParam = searchParams.get('location')
    const typeParam = searchParams.get('type')

    if (locationParam) {
      setSearchLocation(locationParam)
    }
    if (typeParam && typeParam !== 'all') {
      // Map care type to search name if needed
      setSearchName(typeParam)
    }
  }, [searchParams])

  // Single useEffect for fetching caregivers - runs only once on mount
  useEffect(() => {
    let isMounted = true;
    
    const fetchCaregivers = async () => {
      try {
        setLoading(true)
        setError(null)
        console.log('Fetching caregivers from database...')
        const data = await dataService.getCaregivers()
        
        // Only update state if component is still mounted
        if (isMounted) {
          console.log('Caregivers data received:', data?.length || 0, 'items')
          setCaregivers(data || [])
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error loading caregivers:', err)
          setError(err instanceof Error ? err.message : 'Failed to load caregivers')
          setCaregivers([])
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchCaregivers();
    
    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    }
  }, []) // Empty dependency array - runs only once on mount

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 border-3 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Loading Caregivers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding the best care providers for you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-3 px-8 py-6 rounded-xl shadow-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
            <div className="w-8 h-8 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)' }}>
              <Search className="w-5 h-5" style={{ color: 'var(--primary)' }} />
            </div>
            <div>
              <div className="text-xl font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Unable to Load Caregivers</div>
              <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>{error}</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Filter and sort caregivers based on search criteria
  const filteredCaregivers = caregivers
    .filter(caregiver => {
      const matchesName = !searchName || caregiver.name.toLowerCase().includes(searchName.toLowerCase())
      const matchesLocation = !searchLocation || caregiver.location.toLowerCase().includes(searchLocation.toLowerCase())
      const matchesRate = !caregiver.hourly_rate || caregiver.hourly_rate <= maxRate

      return matchesName && matchesLocation && matchesRate
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return (b.rating || 0) - (a.rating || 0)
        case 'rate':
          return (a.hourly_rate || 999) - (b.hourly_rate || 999)
        case 'experience':
          return (b.years_experience || 0) - (a.years_experience || 0)
        default:
          return a.name.localeCompare(b.name)
      }
    })

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header Section */}
      <div className="px-8 py-12" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>Find Your Perfect Caregiver</h1>
          <p className="text-xl mb-2" style={{ color: 'var(--text-secondary)' }}>Connect with trusted, verified caregivers for professional home care services</p>
          <div className="flex items-center justify-center gap-2 text-sm" style={{ color: 'var(--text-muted)' }}>
            <Shield className="w-4 h-4" />
            <span>All caregivers are background-checked and verified</span>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="px-8 py-6" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Search caregivers..."
                value={searchName}
                onChange={(e) => setSearchName(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = 'var(--primary)'
                  e.target.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'var(--border-medium)'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            {/* Location Filter */}
            <div className="md:w-48 relative">
              <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Location"
                value={searchLocation}
                onChange={(e) => setSearchLocation(e.target.value)}
                className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = 'var(--primary)'
                  e.target.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'var(--border-medium)'
                  e.target.style.boxShadow = 'none'
                }}
              />
            </div>

            {/* Max Rate Filter */}
            <div className="md:w-36">
              <select
                value={maxRate}
                onChange={(e) => setMaxRate(Number(e.target.value))}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = 'var(--primary)'
                  e.target.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'var(--border-medium)'
                  e.target.style.boxShadow = 'none'
                }}
              >
                <option value={50}>Up to $50/hr</option>
                <option value={100}>Up to $100/hr</option>
                <option value={150}>Up to $150/hr</option>
                <option value={200}>Up to $200/hr</option>
                <option value={999}>Any rate</option>
              </select>
            </div>

            {/* Sort By */}
            <div className="md:w-44">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-medium)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.target.style.borderColor = 'var(--primary)'
                  e.target.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.target.style.borderColor = 'var(--border-medium)'
                  e.target.style.boxShadow = 'none'
                }}
              >
                <option value="name">Sort by Name</option>
                <option value="rating">Sort by Rating</option>
                <option value="rate">Sort by Rate</option>
                <option value="experience">Sort by Experience</option>
              </select>
            </div>
          </div>

          {/* Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p style={{ color: 'var(--text-secondary)' }}>
              {filteredCaregivers.length} caregiver{filteredCaregivers.length !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>
      </div>

      {/* Caregivers Grid */}
      <div className="px-8 py-6">
        <div className="max-w-6xl mx-auto">
          {filteredCaregivers.length === 0 ? (
            <div className="text-center py-12">
              <Users className="mx-auto mb-4 w-16 h-16" style={{ color: 'var(--text-muted)' }} />
              <h3 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>No caregivers found</h3>
              <p style={{ color: 'var(--text-secondary)' }}>Try adjusting your search criteria or filters</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredCaregivers.map((caregiver) => (
                <div
                  key={caregiver.id}
                  className="rounded-lg p-6 transition-shadow duration-200 hover:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)'
                  }}
                >
                  {/* Profile Header */}
                  <div className="flex items-start mb-4">
                    <div className="flex-shrink-0 mr-4">
                      {caregiver.profile_image ? (
                        <img
                          src={caregiver.profile_image}
                          alt={caregiver.name || 'Caregiver'}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-16 h-16 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                          <Shield className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>
                        {caregiver.name}
                      </h3>
                      <div className="flex items-center mb-2">
                        <MapPin className="w-4 h-4 mr-1" style={{ color: 'var(--text-muted)' }} />
                        <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {caregiver.location}
                        </span>
                      </div>

                      {/* Rating Display */}
                      {caregiver.rating && (
                        <div className="flex items-center mb-2">
                          <Star className="w-4 h-4 mr-1" style={{ color: 'var(--warning)', fill: 'var(--warning)' }} />
                          <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                            {caregiver.rating.toFixed(1)}
                          </span>
                          <span className="text-sm ml-1" style={{ color: 'var(--text-secondary)' }}>
                            ({caregiver.reviews_count || 0} reviews)
                          </span>
                        </div>
                      )}

                      {/* Availability Status */}
                      {caregiver.availability_status && (
                        <div className="flex items-center mb-2">
                          <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)' }}></div>
                          <span className="text-sm" style={{ color: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-secondary)' }}>
                            {caregiver.availability_status === 'available' ? 'Available Today' : 'Contact for Availability'}
                          </span>
                        </div>
                      )}

                      {caregiver.verified && (
                        <div className="flex items-center">
                          <Shield className="w-4 h-4 mr-1" style={{ color: 'var(--primary)' }} />
                          <span className="text-sm font-medium" style={{ color: 'var(--primary)' }}>
                            Verified Professional
                          </span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Bio */}
                  <p className="text-sm mb-4 line-clamp-3" style={{ color: 'var(--text-secondary)' }}>
                    {caregiver.bio}
                  </p>

                  {/* Specialties */}
                  {caregiver.specialties && caregiver.specialties.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-2">
                        {caregiver.specialties.slice(0, 3).map((specialty: string, index: number) => (
                          <span
                            key={index}
                            className="px-3 py-1 rounded-full text-xs font-medium"
                            style={{
                              backgroundColor: 'var(--bg-accent)',
                              color: 'var(--primary)'
                            }}
                          >
                            {specialty}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Rate and Experience */}
                  <div className="flex items-center justify-between mb-4 text-sm" style={{ color: 'var(--text-secondary)' }}>
                    {caregiver.hourly_rate && (
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        <span>${caregiver.hourly_rate}/hr</span>
                      </div>
                    )}
                    {caregiver.years_experience && (
                      <div className="flex items-center">
                        <span>{caregiver.years_experience}+ years exp</span>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <button
                      className="flex-1 py-2 px-4 rounded-lg font-medium transition-colors"
                      style={{
                        backgroundColor: 'var(--primary)',
                        color: 'white'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--primary-dark)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--primary)'
                      }}
                      onClick={() => navigate(`/provider/caregivers/${caregiver.id}`)}
                    >
                      View Profile
                    </button>
                    <button
                      className="p-2 rounded-lg transition-colors"
                      style={{
                        backgroundColor: favorites.includes(caregiver.id) ? 'var(--bg-accent)' : 'var(--bg-secondary)',
                        color: favorites.includes(caregiver.id) ? 'var(--error)' : 'var(--text-primary)',
                        border: '1px solid var(--border-medium)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = favorites.includes(caregiver.id) ? 'var(--bg-accent)' : 'var(--bg-secondary)'
                      }}
                      onClick={() => toggleFavorite(caregiver.id)}
                      title={favorites.includes(caregiver.id) ? 'Remove from favorites' : 'Add to favorites'}
                    >
                      <Heart className={`w-4 h-4 ${favorites.includes(caregiver.id) ? 'fill-current' : ''}`} />
                    </button>
                    <button
                      className="p-2 rounded-lg transition-colors"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-medium)'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                      }}
                      onClick={() => navigate('/messages', { state: { startConversationWith: caregiver.id, providerName: caregiver.name } })}
                      title={`Send message to ${caregiver.name}`}
                    >
                      <MessageCircle className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
