import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Search, Users, Sparkles, Calendar, MessageSquare, CheckSquare, Shield, UserCheck, ChevronDown, CheckCircle, Award } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Home() {
  const [showFindCareDropdown, setShowFindCareDropdown] = useState(false)
  const [caregivers, setCaregivers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [homepageStats, setHomepageStats] = useState({
    verifiedProfessionals: 0,
    averageRating: null,
    successfulBookings: 0,
    supportStatus: null
  })
  const [searchFilters, setSearchFilters] = useState({
    careType: 'all',
    location: '',
    availability: 'any',
    insurance: 'any',
    language: 'any',
    certification: 'any'
  })
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      try {
        console.log('Fetching homepage data...')

        // Fetch caregivers and homepage statistics
        const [caregiversData, statsData] = await Promise.all([
          dataService.getCaregivers(),
          dataService.getHomepageStats()
        ])

        console.log('Caregivers data received:', caregiversData)
        console.log('Homepage stats received:', statsData)

        setCaregivers(caregiversData.slice(0, 3)) // Show only first 3 for homepage
        setHomepageStats(statsData)
      } catch (error) {
        console.error('Error fetching homepage data:', error)
        setCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  const handleGetStarted = () => {
    navigate('/get-started')
  }

  const handleBrowseProviders = () => {
    navigate('/caregivers')
  }

  const handleSearchSubmit = () => {
    // Build search parameters from filters
    const searchParams = new URLSearchParams()

    if (searchFilters.careType !== 'all') {
      searchParams.set('type', searchFilters.careType)
    }

    if (searchFilters.location.trim()) {
      searchParams.set('location', searchFilters.location.trim())
    }

    if (searchFilters.availability !== 'any') {
      searchParams.set('availability', searchFilters.availability)
    }

    if (searchFilters.insurance !== 'any') {
      searchParams.set('insurance', searchFilters.insurance)
    }

    if (searchFilters.language !== 'any') {
      searchParams.set('language', searchFilters.language)
    }

    if (searchFilters.certification !== 'any') {
      searchParams.set('certification', searchFilters.certification)
    }

    // Navigate to caregivers page with search parameters
    const searchQuery = searchParams.toString()
    navigate(`/caregivers${searchQuery ? `?${searchQuery}` : ''}`)
  }

  const handleFilterChange = (filterType: string, value: string) => {
    setSearchFilters(prev => ({
      ...prev,
      [filterType]: value
    }))
  }

  const handleAIHelper = () => {
    // Navigate to AI assistance page or open AI chat
    navigate('/ai-assistant')
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Hero Section - Enhanced with Entrance Effects */}
      <section className="px-8 py-20 text-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-4xl mx-auto">
          <h1 className="text-4xl sm:text-5xl lg:text-6xl font-semibold mb-12 leading-tight tracking-tight" style={{ color: 'var(--text-primary)', fontWeight: '600', letterSpacing: '-0.025em' }}>
            Professional Care Network
          </h1>
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-light mb-10" style={{ color: 'var(--primary)', fontWeight: 'normal', lineHeight: '1.4', marginTop: '2rem' }}>
            Trusted
            <span style={{ margin: '0 16px', color: 'var(--text-muted)', fontSize: '0.8em' }}>|</span>
            Verified
            <span style={{ margin: '0 16px', color: 'var(--text-muted)', fontSize: '0.8em' }}>|</span>
            Connected
          </h2>

          <p className="text-xl sm:text-2xl mb-16 max-w-3xl mx-auto" style={{ 
            color: 'var(--text-secondary)', 
            fontWeight: '400',
            lineHeight: '1.6',
            letterSpacing: '-0.02em',
            fontSize: 'clamp(1.25rem, 2.5vw, 1.75rem)'
          }}>
            Connect with certified healthcare professionals and build your comprehensive care support system with Apple-grade elegance.
          </p>
        </div>

        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
          {/* Find Care Dropdown */}
          <div className="relative">
            <button
              onClick={() => setShowFindCareDropdown(!showFindCareDropdown)}
              className="button-primary flex items-center gap-3 px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 active:scale-95"
              style={{
                boxShadow: 'var(--shadow-light)',
                background: `linear-gradient(135deg, var(--primary) 0%, var(--primary) 100%)`,
              }}
              aria-label="Find care providers"
              aria-expanded={showFindCareDropdown}
              onMouseEnter={(e) => {
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
                e.currentTarget.style.transform = 'translateY(-1px) scale(1.02)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.boxShadow = 'var(--shadow-light)';
                e.currentTarget.style.transform = 'translateY(0) scale(1)';
              }}
            >
              Find Care
              <ChevronDown className={`w-5 h-5 transition-transform duration-300 ${showFindCareDropdown ? 'rotate-180' : ''}`} />
            </button>

            {showFindCareDropdown && (
              <div
                className="absolute top-full left-0 mt-2 w-64 rounded-lg shadow-lg z-50"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  border: '1px solid var(--border-light)'
                }}
              >
                <div className="py-2">
                  <Link
                    to="/caregivers"
                    className="dropdown-link block px-4 py-3 transition-colors"
                    onClick={() => setShowFindCareDropdown(false)}
                  >
                    <div className="flex items-center gap-3">
                      <Users className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Caregivers</div>
                        <div className="text-sm" style={{ color: 'var(--text-muted)' }}>Professional caregivers</div>
                      </div>
                    </div>
                  </Link>
                  <Link
                    to="/companions"
                    className="dropdown-link block px-4 py-3 transition-colors"
                    onClick={() => setShowFindCareDropdown(false)}
                  >
                    <div className="flex items-center gap-3">
                      <Users className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Companions</div>
                        <div className="text-sm" style={{ color: 'var(--text-muted)' }}>Companion care</div>
                      </div>
                    </div>
                  </Link>
                  <Link
                    to="/professionals"
                    className="dropdown-link block px-4 py-3 transition-colors"
                    onClick={() => setShowFindCareDropdown(false)}
                  >
                    <div className="flex items-center gap-3">
                      <Shield className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Professionals</div>
                        <div className="text-sm" style={{ color: 'var(--text-muted)' }}>Healthcare professionals</div>
                      </div>
                    </div>
                  </Link>
                  <Link
                    to="/care-checkers"
                    className="dropdown-link block px-4 py-3 transition-colors"
                    onClick={() => setShowFindCareDropdown(false)}
                  >
                    <div className="flex items-center gap-3">
                      <CheckSquare className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Care Checkers</div>
                        <div className="text-sm" style={{ color: 'var(--text-muted)' }}>Quality assurance</div>
                      </div>
                    </div>
                  </Link>
                </div>
              </div>
            )}
          </div>

          <Link
            to="/how-it-works"
            className="button-secondary px-8 py-4 rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 active:scale-95"
            style={{
              boxShadow: 'var(--shadow-light)',
              border: '1px solid var(--border-medium)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-medium)';
              e.currentTarget.style.transform = 'translateY(-1px) scale(1.02)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-light)';
              e.currentTarget.style.transform = 'translateY(0) scale(1)';
            }}
          >
            Learn More →
          </Link>
        </div>
      </section>

      {/* Trust Indicators Section - Enhanced */}
      <section className="py-12 px-4 sm:px-8" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-1 text-center">
            <div className="px-6 py-4 relative">
              <div className="text-2xl font-bold mb-2" style={{ color: 'var(--primary)' }}>
                {homepageStats.verifiedProfessionals > 0 ? `${homepageStats.verifiedProfessionals.toLocaleString()}+` : '500+'}
              </div>
              <div className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Verified Professionals</div>
              <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-px h-8 bg-gradient-to-b from-transparent to-transparent hidden md:block" style={{ background: `linear-gradient(to bottom, transparent, var(--border-light), transparent)` }}></div>
            </div>
            <div className="px-6 py-4 relative">
              <div className="text-2xl font-bold mb-2" style={{ color: 'var(--primary)' }}>
                {homepageStats.averageRating ? `${homepageStats.averageRating}★` : '4.8★'}
              </div>
              <div className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Average Rating</div>
            </div>
            <div className="px-6 py-4 relative">
              <div className="text-2xl font-bold mb-2" style={{ color: 'var(--primary)' }}>
                {homepageStats.successfulBookings > 0 ? `${homepageStats.successfulBookings.toLocaleString()}+` : '10,000+'}
              </div>
              <div className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Successful Bookings</div>
            </div>
            <div className="px-6 py-4 relative">
              <div className="text-2xl font-bold mb-2" style={{ color: 'var(--primary)' }}>
                {homepageStats.supportStatus || '24/7'}
              </div>
              <div className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Support Available</div>
            </div>
          </div>

          {/* Security Badges - Dynamic Compliance Status */}
          <div className="flex items-center justify-center gap-6 mb-16">
            <div className="flex items-center gap-2.5 px-4 py-2.5 rounded-lg shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
              <CheckCircle className="w-5 h-5" style={{ color: 'var(--primary)' }} />
              <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>HIPAA Compliant</span>
            </div>
            <div className="flex items-center gap-2.5 px-4 py-2.5 rounded-lg shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
              <Shield className="w-5 h-5" style={{ color: 'var(--primary)' }} />
              <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>Background Verified</span>
            </div>
            <div className="flex items-center gap-2.5 px-4 py-2.5 rounded-lg shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
              <Award className="w-5 h-5" style={{ color: 'var(--primary)' }} />
              <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>Licensed & Insured</span>
            </div>
          </div>
        </div>
      </section>

      {/* Advanced Search Section */}
      <section className="py-16 px-4 sm:px-8" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-2xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Find Your Perfect Care Match
            </h2>
            <p className="text-lg text-center mb-10" style={{ color: 'var(--text-secondary)' }}>
            Search by location, specialty, availability, and more
          </p>
          </div>

          <div className="rounded-xl p-8 border" style={{ backgroundColor: 'var(--bg-secondary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-light)' }}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  What type of care?
                </label>
                <select
                  value={searchFilters.careType}
                  onChange={(e) => handleFilterChange('careType', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                >
                  <option value="all">All Care Types</option>
                  <option value="caregivers">Caregivers</option>
                  <option value="companions">Companions</option>
                  <option value="professionals">Healthcare Professionals</option>
                  <option value="care-checkers">Care Checkers</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Location
                </label>
                <input
                  type="text"
                  placeholder="Enter city or zip code"
                  value={searchFilters.location}
                  onChange={(e) => handleFilterChange('location', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Availability
                </label>
                <select
                  value={searchFilters.availability}
                  onChange={(e) => handleFilterChange('availability', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                >
                  <option value="any">Any Time</option>
                  <option value="weekdays">Weekdays</option>
                  <option value="weekends">Weekends</option>
                  <option value="24-7">24/7 Available</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Insurance Accepted
                </label>
                <select
                  value={searchFilters.insurance}
                  onChange={(e) => handleFilterChange('insurance', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                >
                  <option value="any">Any Insurance</option>
                  <option value="medicare">Medicare</option>
                  <option value="medicaid">Medicaid</option>
                  <option value="private">Private Insurance</option>
                  <option value="self-pay">Self-Pay</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Languages Spoken
                </label>
                <select
                  value={searchFilters.language}
                  onChange={(e) => handleFilterChange('language', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                >
                  <option value="any">Any Language</option>
                  <option value="english">English</option>
                  <option value="spanish">Spanish</option>
                  <option value="mandarin">Mandarin</option>
                  <option value="french">French</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Certifications
                </label>
                <select
                  value={searchFilters.certification}
                  onChange={(e) => handleFilterChange('certification', e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-opacity-50"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                >
                  <option value="any">Any Certification</option>
                  <option value="cna">CNA Certified</option>
                  <option value="rn">Registered Nurse</option>
                  <option value="lpn">Licensed Practical Nurse</option>
                  <option value="cpr">CPR Certified</option>
                </select>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={handleSearchSubmit}
                className="w-full px-5 py-2.5 rounded-lg text-sm font-medium transition-all duration-200 hover:shadow-sm active:scale-[0.98]"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'white'
                }}
              >
                <Search className="w-5 h-5 inline mr-3" />
                Search Care Providers
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Your Complete Care Network Section */}
      <section className="py-20 px-4 sm:px-8 text-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-6xl mx-auto">
          <h2 className="text-3xl sm:text-4xl font-bold mb-6" style={{ color: 'var(--text-primary)' }}>
            Your Complete Care Network
          </h2>
          <p className="text-lg mb-16 max-w-2xl mx-auto" style={{ color: 'var(--text-secondary)' }}>
            Healthcare coordination for modern families with comprehensive tools and verified professionals.
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Find Verified Care */}
          <div
            className="rounded-lg p-8 shadow-sm"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <Search className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Find Verified Care
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Connect with rigorously vetted healthcare professionals who meet our highest standards for expertise, reliability, and compassionate care delivery.
            </p>
            <button
              onClick={handleBrowseProviders}
              className="w-full py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                border: '1px solid var(--border-medium)'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
            >
              Browse Providers
              <Search className="w-4 h-4" />
            </button>
          </div>

          {/* Build Your Care Group */}
          <div
            className="rounded-lg p-8 shadow-sm"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <Users className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Build Your Care Group
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Orchestrate seamless collaboration between family members, caregivers, and healthcare professionals through our sophisticated care coordination platform.
            </p>
            <Link 
              to="/how-it-works" 
              className="inline-flex items-center gap-1.5 text-primary no-underline text-sm font-medium transition-all duration-200 px-3 py-2 rounded-md hover:bg-black/5"
            >
              New Group →
            </Link>
          </div>

          {/* AI-Powered Assistance */}
          <div
            className="rounded-lg p-8 shadow-sm"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <Sparkles className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              AI-Powered Assistance
            </h3>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>
              Experience next-generation healthcare coordination with intelligent automation that anticipates needs, optimizes schedules, and enhances care outcomes.
            </p>
            <button
              onClick={handleAIHelper}
              className="w-full py-3 px-4 rounded-lg font-medium transition-colors"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                border: '1px solid var(--border-medium)'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
            >
              AI Helper →
            </button>
          </div>
        </div>
      </section>

      {/* Essential Care Tools */}
      <section className="py-16 px-8 text-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <h2 className="text-3xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
          Essential Care Tools
        </h2>
        <p className="mb-12" style={{ color: 'var(--text-secondary)' }}>
          Professional tools for care coordination.
        </p>

        <div className="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Shared Calendars */}
          <Link
            to="/shared-calendars"
            className="rounded-lg p-8 shadow-sm block transition-colors"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <Calendar className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Shared Calendars
            </h3>
          </Link>

          {/* Secure Messaging */}
          <Link
            to="/secure-messaging"
            className="rounded-lg p-8 shadow-sm block transition-colors"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <MessageSquare className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Secure Messaging
            </h3>
          </Link>

          {/* Task Management */}
          <Link
            to="/task-management"
            className="rounded-lg p-8 shadow-sm block transition-colors"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
          >
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
              style={{ backgroundColor: 'var(--bg-accent)' }}
            >
              <CheckSquare className="w-8 h-8" style={{ color: 'var(--primary)' }} />
            </div>
            <h3 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
              Task Management
            </h3>
          </Link>
        </div>
      </section>

      {/* Provider Cards Section - REAL DATA FROM DATABASE */}
      <section className="py-16" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="max-w-4xl mx-auto px-6">
          <h2 className="text-3xl font-bold mb-6 text-center" style={{ color: 'var(--text-primary)' }}>
            Featured Care Providers
          </h2>
          <p className="text-center mb-8" style={{ color: 'var(--text-secondary)' }}>
            Meet our top-rated, verified healthcare professionals ready to provide exceptional care.
          </p>

          {loading ? (
            <div className="text-center py-16">
              <div className="max-w-sm mx-auto">
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full border-2 border-transparent animate-spin"
                         style={{ borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)' }}></div>
                    <div className="absolute inset-0 w-12 h-12 rounded-full border-2 opacity-20"
                         style={{ borderColor: 'var(--primary)' }}></div>
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                  Connecting to Provider Network
                </h3>
                <p className="text-sm mb-3" style={{ color: 'var(--text-secondary)' }}>
                  Searching verified healthcare professionals in your area...
                </p>
                <div className="flex items-center justify-center gap-2 text-xs" style={{ color: 'var(--text-muted)' }}>
                  <span>•</span>
                  <span>Verifying credentials</span>
                  <span>•</span>
                  <span>Checking availability</span>
                  <span>•</span>
                  <span>Matching preferences</span>
                </div>
              </div>
            </div>
          ) : caregivers.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {caregivers.map((caregiver) => (
                  <div
                    key={caregiver.id}
                    className="rounded-2xl p-8 transition-all duration-300 border cursor-pointer"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-light)',
                      boxShadow: 'var(--shadow-light)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    }}
                  >
                    {/* Header with Avatar and Verification */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <div
                          className="w-14 h-14 rounded-full flex items-center justify-center"
                          style={{ backgroundColor: 'var(--bg-accent)' }}
                        >
                          {caregiver.avatar_url ? (
                            <img
                              src={caregiver.avatar_url}
                              alt={caregiver.full_name || 'Provider'}
                              className="w-14 h-14 rounded-full object-cover"
                            />
                          ) : (
                            <Users className="w-7 h-7" style={{ color: 'var(--primary)' }} />
                          )}
                        </div>
                        {/* Verification Badge */}
                        <div className="absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center"
                             style={{ backgroundColor: 'var(--primary)' }}>
                          <Shield className="w-3 h-3" style={{ color: 'var(--bg-primary)' }} />
                        </div>
                      </div>

                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-1" style={{ color: 'var(--text-primary)' }}>
                          {caregiver.full_name || 'Professional Caregiver'}
                        </h3>

                        {/* Rating Display */}
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex items-center">
                            {caregiver.average_rating ? [1, 2, 3, 4, 5].map((star) => (
                              <span key={star} className="text-sm" style={{ color: star <= caregiver.average_rating ? 'var(--primary)' : 'var(--border-medium)' }}>
                                ★
                              </span>
                            )) : (
                              <span className="text-sm" style={{ color: 'var(--text-muted)' }}>Not rated</span>
                            )}
                          </div>
                          <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>
                            {caregiver.average_rating ? `${caregiver.average_rating.toFixed(1)}★` : 'New provider'} {caregiver.reviews_count > 0 && `(${caregiver.reviews_count} reviews)`}
                          </span>
                        </div>

                        {/* Availability Indicator */}
                        {caregiver.availability_status && (
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full" style={{ backgroundColor: caregiver.availability_status === 'available' ? 'var(--primary)' : 'var(--text-muted)' }}></div>
                            <span className="text-sm font-medium" style={{ color: caregiver.availability_status === 'available' ? 'var(--primary)' : 'var(--text-muted)' }}>
                              {caregiver.availability_status === 'available' ? 'Available' : 'Busy'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Bio */}
                    {caregiver.bio && (
                      <p className="text-sm mb-4 leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
                        {caregiver.bio.length > 120 ? `${caregiver.bio.substring(0, 120)}...` : caregiver.bio}
                      </p>
                    )}
                    <div className="text-center mb-4">
                      {caregiver.location && (
                        <div className="flex items-center justify-center gap-1 mb-2" style={{ color: 'var(--text-secondary)' }}>
                          <span>📍</span>
                          <span>{caregiver.location}</span>
                        </div>
                      )}
                      {caregiver.specialties && (
                        <div className="flex gap-2 justify-center flex-wrap">
                          {caregiver.specialties.slice(0, 2).map((specialty: string, index: number) => (
                            <span
                              key={index}
                              className="px-2 py-1 rounded text-sm"
                              style={{
                                backgroundColor: 'var(--bg-accent)',
                                color: 'var(--primary)'
                              }}
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <Link
                      to={`/provider/caregiver/${caregiver.id}`}
                      className="w-full py-2 px-4 rounded-lg font-medium transition-colors block text-center"
                      style={{
                        backgroundColor: 'var(--primary)',
                        color: 'var(--bg-primary)'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                      onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                    >
                      View Profile
                    </Link>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link
                  to="/caregivers"
                  className="px-6 py-3 rounded-lg font-medium transition-colors inline-block"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                  onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                >
                  View All Caregivers →
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{ backgroundColor: 'var(--bg-accent)' }}>
                  <Search className="w-8 h-8" style={{ color: 'var(--primary)' }} />
                </div>
                <h3 className="text-xl font-semibold mb-3" style={{ color: 'var(--text-primary)' }}>
                  Building Our Provider Network
                </h3>
                <p className="text-base mb-6" style={{ color: 'var(--text-secondary)' }}>
                  We're carefully vetting and onboarding qualified care providers to ensure the highest quality of service.
                </p>
                <button
                  onClick={handleBrowseProviders}
                  className="px-6 py-3 rounded-lg font-medium transition-all duration-200"
                  style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    border: 'none'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                  onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                >
                  Join Our Provider Network
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Take Control Section */}
      <section className="py-16 px-8 text-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <h2 className="text-4xl font-bold mb-4" style={{ color: 'var(--text-primary)' }}>
          Take <span style={{ color: 'var(--primary)' }}>Control</span> of Your Care
        </h2>
        <p className="mb-8" style={{ color: 'var(--text-secondary)' }}>
          Connect with care professionals.
        </p>

        <div className="flex gap-4 justify-center flex-wrap">
          <button
            onClick={handleGetStarted}
            className="px-6 py-3 rounded-lg font-medium transition-colors"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)'
            }}
            onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
            onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
          >
            Get Started →
          </button>

          <button
            onClick={handleBrowseProviders}
            className="px-6 py-3 rounded-lg font-medium flex items-center gap-2 transition-colors"
            style={{
              backgroundColor: 'var(--bg-primary)',
              color: 'var(--text-primary)',
              border: '1px solid var(--border-medium)'
            }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
          >
            <Search className="w-4 h-4" />
            Browse Providers
          </button>
        </div>
      </section>

      {/* Footer - Enhanced Spacing & Alignment */}
      <footer
        className="py-20 px-8"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12 mb-12">
            {/* Logo and Description - Enhanced */}
            <div className="lg:col-span-1">
              <div className="flex items-center gap-3 mb-6">
                <div
                  className="w-10 h-10 rounded-xl flex items-center justify-center shadow-sm"
                  style={{ backgroundColor: 'var(--primary)' }}
                >
                  <span className="font-bold text-base" style={{ color: 'var(--bg-primary)' }}>CC</span>
                </div>
                <span className="font-bold text-2xl" style={{ color: 'var(--text-primary)' }}>Care Connector</span>
              </div>
              <p className="mb-6 text-base leading-relaxed max-w-md" style={{ color: 'var(--text-secondary)' }}>
                Modern healthcare coordination platform connecting families with verified care professionals through intelligent automation and secure collaboration tools.
              </p>
              <div className="flex flex-wrap gap-6">
                <div className="flex items-center gap-2 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <Shield className="w-4 h-4" style={{ color: 'var(--primary)' }} />
                  <span className="font-medium">HIPAA Compliant</span>
                </div>
                <div className="flex items-center gap-2 text-sm" style={{ color: 'var(--text-secondary)' }}>
                  <UserCheck className="w-4 h-4" style={{ color: 'var(--primary)' }} />
                  <span className="font-medium">Verified Providers</span>
                </div>
              </div>
            </div>

            {/* Services - Enhanced */}
            <div>
              <h4 className="font-semibold mb-6 text-lg" style={{ color: 'var(--text-primary)' }}>Services</h4>
              <ul className="space-y-4" style={{ color: 'var(--text-secondary)' }}>
                <li>
                  <Link
                    to="/caregivers"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Find Care
                  </Link>
                </li>
                <li>
                  <Link
                    to="/care-groups"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Care Groups
                  </Link>
                </li>
              </ul>
            </div>

            {/* Learn More - Enhanced */}
            <div>
              <h4 className="font-semibold mb-6 text-lg" style={{ color: 'var(--text-primary)' }}>Learn More</h4>
              <ul className="space-y-4" style={{ color: 'var(--text-secondary)' }}>
                <li>
                  <Link
                    to="/how-it-works"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link
                    to="/features"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>

            {/* Legal & Compliance - New Section */}
            <div>
              <h4 className="font-semibold mb-6 text-lg" style={{ color: 'var(--text-primary)' }}>Legal & Compliance</h4>
              <ul className="space-y-4" style={{ color: 'var(--text-secondary)' }}>
                <li>
                  <Link
                    to="/privacy-policy"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    to="/terms-of-service"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Terms of Service
                  </Link>
                </li>
                <li>
                  <Link
                    to="/hipaa-notice"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    HIPAA Notice
                  </Link>
                </li>
                <li>
                  <Link
                    to="/provider-portal"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Provider Portal
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div
            className="pt-8 text-center"
            style={{
              borderTop: '1px solid var(--border-light)',
              color: 'var(--text-secondary)'
            }}
          >
            <p className="text-sm">© {new Date().getFullYear()} Care Connector. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
