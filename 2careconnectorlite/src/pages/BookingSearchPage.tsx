import React, { useState, useEffect } from 'react'
import { Search, Filter, Calendar, MapPin, DollarSign, Clock, Star, ChevronDown, X } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface SearchFilters {
  query: string
  dateRange: { start: string; end: string }
  location: string
  priceRange: { min: number; max: number }
  timeSlot: string
  providerType: string
  rating: number
  availability: string
}

interface SearchResult {
  id: string
  provider_name: string
  provider_type: 'caregiver' | 'companion' | 'care_checker'
  location: string
  rate: number
  rating: number
  availability: string[]
  profile_image: string
  specialties: string[]
  description: string
}

const BookingSearchPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [searching, setSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<SearchResult[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const [resultCount, setResultCount] = useState(0)

  const [filters, setFilters] = useState<SearchFilters>({
    query: '',
    dateRange: { start: '', end: '' },
    location: '',
    priceRange: { min: 0, max: 200 },
    timeSlot: '',
    providerType: '',
    rating: 0,
    availability: ''
  })

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      setLoading(false)
    }
    getUser()
  }, [])

  const performSearch = async () => {
    if (!user) return
    
    setSearching(true)
    try {
      let query = supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          role,
          location,
          hourly_rate,
          rating,
          avatar_url,
          bio,
          specialties
        `)
        .in('role', ['caregiver', 'companion', 'care_checker'])

      if (filters.query) {
        query = query.or(`full_name.ilike.%${filters.query}%,bio.ilike.%${filters.query}%,specialties.ilike.%${filters.query}%`)
      }

      if (filters.location) {
        query = query.ilike('location', `%${filters.location}%`)
      }

      if (filters.providerType) {
        query = query.eq('role', filters.providerType)
      }

      if (filters.priceRange.min > 0 || filters.priceRange.max < 200) {
        query = query.gte('hourly_rate', filters.priceRange.min).lte('hourly_rate', filters.priceRange.max)
      }

      if (filters.rating > 0) {
        query = query.gte('rating', filters.rating)
      }

      const { data, error } = await query.limit(50)

      if (error) throw error

      const results = data?.map(profile => ({
        id: profile.id,
        provider_name: profile.full_name || 'Unknown Provider',
        provider_type: profile.role as 'caregiver' | 'companion' | 'care_checker',
        location: profile.location || 'Location not specified',
        rate: profile.hourly_rate || 0,
        rating: profile.rating || 0,
        availability: ['Morning', 'Afternoon', 'Evening'],
        profile_image: profile.avatar_url || '',
        specialties: profile.specialties ? [profile.specialties] : [],
        description: profile.bio || 'No description available'
      })) || []

      setSearchResults(results)
      setResultCount(results.length)
    } catch (error) {
      console.error('Search error:', error)
      setSearchResults([])
      setResultCount(0)
    } finally {
      setSearching(false)
    }
  }

  const clearFilters = () => {
    setFilters({
      query: '',
      dateRange: { start: '', end: '' },
      location: '',
      priceRange: { min: 0, max: 200 },
      timeSlot: '',
      providerType: '',
      rating: 0,
      availability: ''
    })
    setSearchResults([])
    setResultCount(0)
  }

  const handleBookProvider = (providerId: string) => {
    window.location.href = `/provider/${filters.providerType || 'caregivers'}/${providerId}`
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="text-gray-600">Loading search...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center max-w-md mx-auto p-8">
          <Search className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Search Unavailable</h1>
          <p className="text-gray-600 mb-6">Please sign in to search for care providers.</p>
          <button
            onClick={() => window.location.href = '/auth'}
            className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
          >
            Sign In to Search
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Find Care Providers</h1>
          <p className="text-gray-600">Search and filter through available caregivers, companions, and care checkers</p>
        </div>

        {/* Search Bar */}
        <div className="rounded-lg shadow-sm p-6 mb-6" style={{ backgroundColor: 'var(--bg-primary)' }}>
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} />
              <input
                type="text"
                placeholder="Search providers, specialties, or services..."
                value={filters.query}
                onChange={(e) => setFilters(prev => ({ ...prev, query: e.target.value }))}
                className="w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:outline-none"
                style={{
                  borderColor: 'var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Filter className="w-5 h-5" />
                Filters
                <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
              </button>
              <button
                onClick={performSearch}
                disabled={searching}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50"
              >
                {searching ? 'Searching...' : 'Search'}
              </button>
            </div>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Location Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <MapPin className="w-4 h-4 inline mr-1" />
                    Location
                  </label>
                  <input
                    type="text"
                    placeholder="Enter city or area"
                    value={filters.location}
                    onChange={(e) => setFilters(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  />
                </div>

                {/* Provider Type Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Provider Type</label>
                  <select
                    value={filters.providerType}
                    onChange={(e) => setFilters(prev => ({ ...prev, providerType: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value="">All Types</option>
                    <option value="caregiver">Caregivers</option>
                    <option value="companion">Companions</option>
                    <option value="care_checker">Care Checkers</option>
                  </select>
                </div>

                {/* Price Range Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <DollarSign className="w-4 h-4 inline mr-1" />
                    Hourly Rate
                  </label>
                  <div className="flex gap-2">
                    <input
                      type="number"
                      placeholder="Min"
                      value={filters.priceRange.min || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        priceRange: { ...prev.priceRange, min: Number(e.target.value) || 0 }
                      }))}
                      className="w-full px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                    <input
                      type="number"
                      placeholder="Max"
                      value={filters.priceRange.max || ''}
                      onChange={(e) => setFilters(prev => ({ 
                        ...prev, 
                        priceRange: { ...prev.priceRange, max: Number(e.target.value) || 200 }
                      }))}
                      className="w-full px-2 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                    />
                  </div>
                </div>

                {/* Rating Filter */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Star className="w-4 h-4 inline mr-1" />
                    Minimum Rating
                  </label>
                  <select
                    value={filters.rating}
                    onChange={(e) => setFilters(prev => ({ ...prev, rating: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent"
                  >
                    <option value={0}>Any Rating</option>
                    <option value={3}>3+ Stars</option>
                    <option value={4}>4+ Stars</option>
                    <option value={4.5}>4.5+ Stars</option>
                  </select>
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={clearFilters}
                  className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  <X className="w-4 h-4" />
                  Clear Filters
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Search Results */}
        <div className="bg-white rounded-lg shadow-sm">
          {/* Results Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-gray-900">
                Search Results {resultCount > 0 && `(${resultCount} providers found)`}
              </h2>
              {searchResults.length > 0 && (
                <div className="text-sm text-gray-600">
                  Showing {searchResults.length} of {resultCount} results
                </div>
              )}
            </div>
          </div>

          {/* Results List */}
          <div className="p-6">
            {searching ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-logo-green mx-auto mb-4"></div>
                <p className="text-gray-600">Searching providers...</p>
              </div>
            ) : searchResults.length > 0 ? (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {searchResults.map((result) => (
                  <div key={result.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start gap-4">
                      <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center">
                        {result.profile_image ? (
                          <img 
                            src={result.profile_image} 
                            alt={result.provider_name}
                            className="w-16 h-16 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-xl font-semibold text-gray-600">
                            {result.provider_name.charAt(0)}
                          </span>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex justify-between items-start mb-2">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900">{result.provider_name}</h3>
                            <p className="text-sm text-gray-600 capitalize">{result.provider_type.replace('_', ' ')}</p>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-logo-green">${result.rate}/hr</div>
                            <div className="flex items-center gap-1 text-sm text-gray-600">
                              <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                              {result.rating.toFixed(1)}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                          <MapPin className="w-4 h-4" />
                          {result.location}
                        </div>

                        <p className="text-sm text-gray-700 mb-3 line-clamp-2">{result.description}</p>

                        {result.specialties.length > 0 && (
                          <div className="flex flex-wrap gap-2 mb-4">
                            {result.specialties.map((specialty, index) => (
                              <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs">
                                {specialty}
                              </span>
                            ))}
                          </div>
                        )}

                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Clock className="w-4 h-4" />
                            Available {result.availability.join(', ')}
                          </div>
                          <button
                            onClick={() => handleBookProvider(result.id)}
                            className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors text-sm"
                          >
                            View Profile
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No providers found</h3>
                <p className="text-gray-600 mb-6">
                  {filters.query || filters.location || filters.providerType ? 
                    'Try adjusting your search criteria or clearing filters' : 
                    'Enter search terms or apply filters to find care providers'}
                </p>
                {(filters.query || filters.location || filters.providerType) && (
                  <button
                    onClick={clearFilters}
                    className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                  >
                    Clear Search
                  </button>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mt-8">
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={() => window.location.href = '/my-bookings'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <Calendar className="w-6 h-6 text-logo-green mb-2" />
                <div className="font-medium text-gray-900">My Bookings</div>
                <div className="text-sm text-gray-600">View your scheduled appointments</div>
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
                <div className="font-medium text-gray-900">Dashboard</div>
                <div className="text-sm text-gray-600">Go to your main dashboard</div>
              </button>
              <button
                onClick={() => window.location.href = '/booking-analytics'}
                className="bg-gray-50 hover:bg-gray-100 p-4 rounded-lg transition-colors text-left"
              >
                <div className="w-6 h-6 bg-logo-green rounded mb-2"></div>
                <div className="font-medium text-gray-900">Analytics</div>
                <div className="text-sm text-gray-600">View booking insights</div>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default BookingSearchPage
